<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dataset Fix Test</title>
</head>
<body>
    <h1>Dataset Fix Test</h1>
    <p>这个测试文件用于验证 dataset.map 错误的修复</p>
    
    <script>
        // 模拟测试不同类型的 dataset 值
        function testDatasetHandling(dataset, testName) {
            console.log(`\n=== 测试: ${testName} ===`);
            console.log('原始 dataset:', dataset);
            
            // 模拟修复后的逻辑
            if (!Array.isArray(dataset)) {
                console.warn('Dataset is not an array:', dataset);
                dataset = [];
            }
            
            try {
                // 测试 map 操作
                const result = dataset.map((item, index) => ({
                    ...item,
                    index: index + 1
                }));
                console.log('处理结果:', result);
                console.log('✅ 测试通过');
            } catch (error) {
                console.error('❌ 测试失败:', error);
            }
        }
        
        // 运行测试
        console.log('开始测试 dataset 处理逻辑...');
        
        // 测试各种情况
        testDatasetHandling(undefined, 'undefined dataset');
        testDatasetHandling(null, 'null dataset');
        testDatasetHandling('not an array', 'string dataset');
        testDatasetHandling(123, 'number dataset');
        testDatasetHandling({}, 'object dataset');
        testDatasetHandling([], '空数组 dataset');
        testDatasetHandling([{name: 'test', value: 100}], '正常数组 dataset');
        
        console.log('\n所有测试完成！');
    </script>
</body>
</html>
