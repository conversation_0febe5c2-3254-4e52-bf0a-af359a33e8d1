# Dataset.map 错误修复总结

## 问题描述
在应用中出现了 `TypeError: dataset.map is not a function` 错误，这是因为在某些情况下 `dataset` 不是数组类型，而代码直接调用了 `.map()` 方法。

## 错误位置
1. `src/packages/components/Tables/Tables/TableScrollBoard/index.vue:222`
2. `src/packages/components/Tables/Tables/TableList/index.vue:76`
3. 其他相关组件中的类似问题

## 修复方案
在所有使用 `dataset.map()` 的地方添加数组类型检查，确保 `dataset` 是数组类型，如果不是则使用空数组作为默认值。

## 修复的文件和函数

### 1. TableScrollBoard 组件
**文件**: `src/packages/components/Tables/Tables/TableScrollBoard/index.vue`

#### calcRowsData 函数 (第210行)
```javascript
// 修复前
let { dataset, index, headerBGC, rowNum } = status.mergedConfig
dataset = dataset.map((row: any, i: number) => { ... })

// 修复后
let { dataset, index, headerBGC, rowNum } = status.mergedConfig
// 确保 dataset 是数组，如果不是则使用空数组
if (!Array.isArray(dataset)) {
  console.warn('Dataset is not an array:', dataset)
  dataset = []
}
dataset = dataset.map((row: any, i: number) => { ... })
```

#### calcHeights 函数 (第255行)
```javascript
// 修复前
if (!onresize) status.heights = new Array(dataset.length).fill(avgHeight)

// 修复后
const datasetLength = Array.isArray(dataset) ? dataset.length : 0
if (!onresize) status.heights = new Array(datasetLength).fill(avgHeight)
```

### 2. TableList 组件
**文件**: `src/packages/components/Tables/Tables/TableList/index.vue`

#### calcRowsData 函数 (第66行)
```javascript
// 修复前
let { dataset, rowNum, sort } = status.mergedConfig
const value = dataset.map(({ value }) => value)

// 修复后
let { dataset, rowNum, sort } = status.mergedConfig
// 确保 dataset 是数组，如果不是则使用空数组
if (!Array.isArray(dataset)) {
  console.warn('Dataset is not an array:', dataset)
  dataset = []
}
const value = dataset.map(({ value }) => value)
```

#### calcHeights 函数 (第105行)
```javascript
// 修复前
if (!onresize) status.heights = new Array(dataset.length).fill(avgHeight)

// 修复后
const datasetLength = Array.isArray(dataset) ? dataset.length : 0
if (!onresize) status.heights = new Array(datasetLength).fill(avgHeight)
```

### 3. TablesBasic 组件
**文件**: `src/packages/components/Tables/Tables/TablesBasic/index.vue`

#### filterData 计算属性 (第53行)
```javascript
// 修复前
const filterData = computed(() => {
  return option?.dataset?.source?.filter((item: any) => { ... })
})

// 修复后
const filterData = computed(() => {
  const source = option?.dataset?.source
  if (!Array.isArray(source)) {
    return []
  }
  return source.filter((item: any) => { ... })
})
```

#### watch 函数 (第79行)
```javascript
// 修复前
option?.dataset?.dimensions?.forEach((header: any) => {
  header.align = align.value
})

// 修复后
if (Array.isArray(option?.dataset?.dimensions)) {
  option.dataset.dimensions.forEach((header: any) => {
    header.align = align.value
  })
}
```

### 4. MapBase 组件
**文件**: `src/packages/components/Charts/Maps/MapBase/index.vue`

#### dataSetHandle 函数 (第116行)
```javascript
// 修复前
else if (item.type === 'lines' && dataset.line) {
  item.data = dataset.line.map((it: any) => { ... })
}

// 修复后
else if (item.type === 'lines' && dataset.line) {
  if (Array.isArray(dataset.line)) {
    item.data = dataset.line.map((it: any) => { ... })
  } else {
    console.warn('dataset.line is not an array:', dataset.line)
    item.data = []
  }
}
```

## 修复原理
1. **类型检查**: 使用 `Array.isArray()` 检查变量是否为数组
2. **默认值**: 如果不是数组，使用空数组 `[]` 作为默认值
3. **错误日志**: 添加 `console.warn()` 记录非数组情况，便于调试
4. **安全处理**: 确保后续的数组方法调用不会出错

## 测试验证
创建了测试文件 `test-dataset-fix.html` 来验证修复逻辑，测试了以下情况：
- `undefined` dataset
- `null` dataset  
- 字符串 dataset
- 数字 dataset
- 对象 dataset
- 空数组 dataset
- 正常数组 dataset

## 预防措施
建议在未来的开发中：
1. 在使用数组方法前始终进行类型检查
2. 为数据提供合理的默认值
3. 添加适当的错误处理和日志记录
4. 考虑使用 TypeScript 的严格类型检查来预防此类问题

## 影响范围
此修复解决了以下错误：
- `initFunction.ts:7 UNHANDLED PROMISE REJECTION: TypeError: dataset.map is not a function`
- `index.vue:222 Uncaught (in promise) TypeError: dataset.map is not a function`

修复后，即使在数据获取失败或数据格式不正确的情况下，组件也能正常运行而不会抛出错误。
